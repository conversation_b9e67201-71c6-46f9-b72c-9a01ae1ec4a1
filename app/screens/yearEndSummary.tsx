import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { getTransactions, getCategories } from '../constants/Storage';
import YearEndSummaryPoster from '../components/YearEndSummaryPoster';
import { useTranslation } from 'react-i18next';
import { useSettings } from '../context/SettingsContext';

interface Transaction {
  id: number;
  type: 'income' | 'expense';
  amount: number;
  category: string;
  categoryIcon: string;
  note: string;
  date: string;
  member_id: number;
  refunded: boolean;
  refund_amount?: number;
  reimbursement_status?: 'none' | 'pending' | 'completed';
  tags?: number[];
  exclude_from_budget?: boolean;
  shopping_platform?: string;
  image_path?: string;
  image_paths?: string[];
}

interface Category {
  id: number;
  name: string;
  icon: string;
  type: 'income' | 'expense';
}

interface YearEndSummaryData {
  year: number;
  totalTransactions: number;
  totalSpent: number;
  totalIncome: number;
  topExpenseCategory: { name: string; amount: number; icon: string };
  topIncomeCategory: { name: string; amount: number; icon: string };
  averageDailyExpense: number;
  savingsRate: number;
  monthlyData: Array<{
    month: number;
    expense: number;
    income: number;
    balance: number;
  }>;
}

const YearEndSummaryScreen: React.FC = () => {
  const { t } = useTranslation();
  const { currency } = useSettings();
  const params = useLocalSearchParams();
  const [loading, setLoading] = useState(true);
  const [summaryData, setSummaryData] = useState<YearEndSummaryData | null>(null);

  // Get year from params or default to current year
  const year = params.year ? parseInt(params.year as string) : new Date().getFullYear();

  useEffect(() => {
    calculateYearEndSummary();
  }, [year]);

  const calculateYearEndSummary = async () => {
    try {
      setLoading(true);

      // Get all transactions for the year
      const startDate = new Date(year, 0, 1).toISOString().split('T')[0];
      const endDate = new Date(year, 11, 31).toISOString().split('T')[0];

      const transactionsResult = await getTransactions({
        startDate,
        endDate,
        pageSize: 10000, // Get all transactions
      });

      const transactions = transactionsResult.transactions;
      const categories = await getCategories();

      // Calculate basic stats
      const totalTransactions = transactions.length;
      const expenseTransactions = transactions.filter(t => t.type === 'expense' && !t.refunded);
      const incomeTransactions = transactions.filter(t => t.type === 'income');

      const totalSpent = expenseTransactions.reduce((sum, t) => sum + t.amount, 0);
      const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);

      // Calculate category stats
      const expenseCategoryStats = new Map<string, { amount: number; icon: string }>();
      const incomeCategoryStats = new Map<string, { amount: number; icon: string }>();

      expenseTransactions.forEach(transaction => {
        const current = expenseCategoryStats.get(transaction.category) || { amount: 0, icon: transaction.categoryIcon };
        expenseCategoryStats.set(transaction.category, {
          amount: current.amount + transaction.amount,
          icon: transaction.categoryIcon
        });
      });

      incomeTransactions.forEach(transaction => {
        const current = incomeCategoryStats.get(transaction.category) || { amount: 0, icon: transaction.categoryIcon };
        incomeCategoryStats.set(transaction.category, {
          amount: current.amount + transaction.amount,
          icon: transaction.categoryIcon
        });
      });

      // Find top categories
      const topExpenseCategory = Array.from(expenseCategoryStats.entries())
        .sort((a, b) => b[1].amount - a[1].amount)[0];
      
      const topIncomeCategory = Array.from(incomeCategoryStats.entries())
        .sort((a, b) => b[1].amount - a[1].amount)[0];

      // Calculate monthly data
      const monthlyData = [];
      for (let month = 0; month < 12; month++) {
        const monthStart = new Date(year, month, 1);
        const monthEnd = new Date(year, month + 1, 0);
        
        const monthTransactions = transactions.filter(t => {
          const transactionDate = new Date(t.date);
          return transactionDate >= monthStart && transactionDate <= monthEnd;
        });

        const monthExpense = monthTransactions
          .filter(t => t.type === 'expense' && !t.refunded)
          .reduce((sum, t) => sum + t.amount, 0);
        
        const monthIncome = monthTransactions
          .filter(t => t.type === 'income')
          .reduce((sum, t) => sum + t.amount, 0);

        monthlyData.push({
          month: month + 1,
          expense: monthExpense,
          income: monthIncome,
          balance: monthIncome - monthExpense
        });
      }

      // Calculate additional metrics
      const daysInYear = new Date(year, 11, 31).getDate() === 31 ? 
        (year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0) ? 366 : 365) : 365;
      const averageDailyExpense = totalSpent / daysInYear;
      const savingsRate = totalIncome > 0 ? ((totalIncome - totalSpent) / totalIncome) * 100 : 0;

      const summaryData: YearEndSummaryData = {
        year,
        totalTransactions,
        totalSpent,
        totalIncome,
        topExpenseCategory: topExpenseCategory ? {
          name: topExpenseCategory[0],
          amount: topExpenseCategory[1].amount,
          icon: topExpenseCategory[1].icon
        } : { name: t('common.none'), amount: 0, icon: '📊' },
        topIncomeCategory: topIncomeCategory ? {
          name: topIncomeCategory[0],
          amount: topIncomeCategory[1].amount,
          icon: topIncomeCategory[1].icon
        } : { name: t('common.none'), amount: 0, icon: '💰' },
        averageDailyExpense,
        savingsRate,
        monthlyData
      };

      setSummaryData(summaryData);
    } catch (error) {
      console.error('Error calculating year-end summary:', error);
      Alert.alert(t('common.error'), t('yearEndSummary.calculationError'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    router.back();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>{t('yearEndSummary.calculating')}</Text>
      </View>
    );
  }

  if (!summaryData) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('yearEndSummary.noData')}</Text>
      </View>
    );
  }

  return (
    <YearEndSummaryPoster 
      data={summaryData} 
      onClose={handleClose}
    />
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default YearEndSummaryScreen;
