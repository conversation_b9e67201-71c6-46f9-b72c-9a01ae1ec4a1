import React, { useRef } from 'react';
import { View, Text, StyleSheet, Dimensions, Share, Alert } from 'react-native';
import { Svg, Rect, Text as SvgText, Circle, Line, Defs, LinearGradient, Stop } from 'react-native-svg';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
// import ViewShot from 'react-native-view-shot';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';

const { width: screenWidth } = Dimensions.get('window');
const posterWidth = screenWidth - 40;
const posterHeight = posterWidth * 1.4; // 7:10 ratio

interface YearEndSummaryData {
  year: number;
  totalTransactions: number;
  totalSpent: number;
  totalIncome: number;
  topExpenseCategory: { name: string; amount: number; icon: string };
  topIncomeCategory: { name: string; amount: number; icon: string };
  averageDailyExpense: number;
  savingsRate: number;
  monthlyData: Array<{
    month: number;
    expense: number;
    income: number;
    balance: number;
  }>;
}

interface YearEndSummaryPosterProps {
  data: YearEndSummaryData;
  onClose: () => void;
}

const YearEndSummaryPoster: React.FC<YearEndSummaryPosterProps> = ({ data, onClose }) => {
  const { t } = useTranslation();
  const viewShotRef = useRef<any>(null);

  const handleShare = async () => {
    try {
      const message = `${t('yearEndSummary.shareMessage', { year: data.year })}

📊 ${t('yearEndSummary.totalTransactions')}: ${data.totalTransactions}
💰 ${t('yearEndSummary.totalIncome')}: ¥${data.totalIncome.toFixed(2)}
💸 ${t('yearEndSummary.totalSpent')}: ¥${data.totalSpent.toFixed(2)}
🏆 ${t('yearEndSummary.topExpenseCategory')}: ${data.topExpenseCategory.name} (¥${data.topExpenseCategory.amount.toFixed(2)})
📈 ${t('yearEndSummary.savingsRate')}: ${data.savingsRate.toFixed(1)}%

${t('yearEndSummary.appPromotion')}`;

      await Share.share({
        message,
        title: t('yearEndSummary.shareTitle', { year: data.year }),
      });
    } catch (error) {
      Alert.alert(t('common.error'), t('yearEndSummary.shareError'));
    }
  };

  const handleSaveImage = async () => {
    try {
      if (!viewShotRef.current) return;

      // Request media library permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.error'), t('yearEndSummary.permissionRequired'));
        return;
      }

      // Capture the view as image
      const uri = await viewShotRef.current.capture();
      
      // Save to media library
      await MediaLibrary.saveToLibraryAsync(uri);
      
      Alert.alert(t('common.success'), t('yearEndSummary.imageSaved'));
    } catch (error) {
      console.error('Error saving image:', error);
      Alert.alert(t('common.error'), t('yearEndSummary.saveError'));
    }
  };

  const handleShareImage = async () => {
    try {
      if (!viewShotRef.current) return;

      // Capture the view as image
      const uri = await viewShotRef.current.capture();
      
      // Share the image
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'image/png',
          dialogTitle: t('yearEndSummary.shareTitle', { year: data.year }),
        });
      } else {
        Alert.alert(t('common.error'), t('yearEndSummary.shareNotAvailable'));
      }
    } catch (error) {
      console.error('Error sharing image:', error);
      Alert.alert(t('common.error'), t('yearEndSummary.shareError'));
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('yearEndSummary.title', { year: data.year })}</Text>
        <View style={styles.placeholder} />
      </View>

      <View ref={viewShotRef} style={styles.posterContainer}>
          <Svg width={posterWidth} height={posterHeight} style={styles.poster}>
            <Defs>
              <LinearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <Stop offset="0%" stopColor="#1a1a2e" stopOpacity="1" />
                <Stop offset="100%" stopColor="#16213e" stopOpacity="1" />
              </LinearGradient>
              <LinearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <Stop offset="0%" stopColor="#ffd700" stopOpacity="1" />
                <Stop offset="100%" stopColor="#ffed4e" stopOpacity="1" />
              </LinearGradient>
            </Defs>
            
            {/* Background */}
            <Rect
              width={posterWidth}
              height={posterHeight}
              fill="url(#backgroundGradient)"
              rx={20}
            />
            
            {/* Header decoration */}
            <Rect
              x={0}
              y={0}
              width={posterWidth}
              height={120}
              fill="url(#accentGradient)"
              rx={20}
              opacity={0.1}
            />
            
            {/* Title */}
            <SvgText
              x={posterWidth / 2}
              y={50}
              fontSize="32"
              fontWeight="bold"
              fill="#ffffff"
              textAnchor="middle"
            >
              {data.year}
            </SvgText>
            <SvgText
              x={posterWidth / 2}
              y={75}
              fontSize="18"
              fill="#ffd700"
              textAnchor="middle"
            >
              {t('yearEndSummary.financialSummary')}
            </SvgText>
            
            {/* Decorative elements */}
            <Circle cx={50} cy={100} r={4} fill="#ffd700" opacity={0.8} />
            <Circle cx={posterWidth - 50} cy={100} r={4} fill="#ffd700" opacity={0.8} />
            <Circle cx={80} cy={120} r={2} fill="#4CAF50" opacity={0.6} />
            <Circle cx={posterWidth - 80} cy={120} r={2} fill="#4CAF50" opacity={0.6} />
            
            {/* Main stats section */}
            <SvgText
              x={posterWidth / 2}
              y={160}
              fontSize="16"
              fill="#cccccc"
              textAnchor="middle"
            >
              {t('yearEndSummary.totalTransactions')}
            </SvgText>
            <SvgText
              x={posterWidth / 2}
              y={185}
              fontSize="36"
              fontWeight="bold"
              fill="#4CAF50"
              textAnchor="middle"
            >
              {data.totalTransactions}
            </SvgText>
            
            {/* Income and Expense */}
            <SvgText
              x={posterWidth / 4}
              y={240}
              fontSize="14"
              fill="#cccccc"
              textAnchor="middle"
            >
              {t('common.income')}
            </SvgText>
            <SvgText
              x={posterWidth / 4}
              y={260}
              fontSize="20"
              fontWeight="bold"
              fill="#4CAF50"
              textAnchor="middle"
            >
              ¥{(data.totalIncome / 1000).toFixed(1)}K
            </SvgText>
            
            <SvgText
              x={(posterWidth * 3) / 4}
              y={240}
              fontSize="14"
              fill="#cccccc"
              textAnchor="middle"
            >
              {t('common.expense')}
            </SvgText>
            <SvgText
              x={(posterWidth * 3) / 4}
              y={260}
              fontSize="20"
              fontWeight="bold"
              fill="#FF5722"
              textAnchor="middle"
            >
              ¥{(data.totalSpent / 1000).toFixed(1)}K
            </SvgText>
            
            {/* Decorative divider */}
            <Line
              x1={40}
              y1={300}
              x2={posterWidth - 40}
              y2={300}
              stroke="#444"
              strokeWidth={2}
            />
            <Circle cx={posterWidth / 2} cy={300} r={4} fill="#ffd700" />
            
            {/* Top categories */}
            <SvgText
              x={posterWidth / 2}
              y={340}
              fontSize="16"
              fill="#cccccc"
              textAnchor="middle"
            >
              {t('yearEndSummary.topExpenseCategory')}
            </SvgText>
            <SvgText
              x={posterWidth / 2}
              y={365}
              fontSize="20"
              fontWeight="bold"
              fill="#ffd700"
              textAnchor="middle"
            >
              {data.topExpenseCategory.icon} {data.topExpenseCategory.name}
            </SvgText>
            <SvgText
              x={posterWidth / 2}
              y={385}
              fontSize="16"
              fill="#ffffff"
              textAnchor="middle"
            >
              ¥{data.topExpenseCategory.amount.toFixed(0)}
            </SvgText>
            
            {/* Savings rate and daily average */}
            <SvgText
              x={posterWidth / 4}
              y={440}
              fontSize="14"
              fill="#cccccc"
              textAnchor="middle"
            >
              {t('yearEndSummary.savingsRate')}
            </SvgText>
            <SvgText
              x={posterWidth / 4}
              y={460}
              fontSize="18"
              fontWeight="bold"
              fill="#9C27B0"
              textAnchor="middle"
            >
              {data.savingsRate.toFixed(1)}%
            </SvgText>
            
            <SvgText
              x={(posterWidth * 3) / 4}
              y={440}
              fontSize="14"
              fill="#cccccc"
              textAnchor="middle"
            >
              {t('yearEndSummary.dailyAverage')}
            </SvgText>
            <SvgText
              x={(posterWidth * 3) / 4}
              y={460}
              fontSize="18"
              fontWeight="bold"
              fill="#2196F3"
              textAnchor="middle"
            >
              ¥{data.averageDailyExpense.toFixed(0)}
            </SvgText>
            
            {/* Footer */}
            <Line
              x1={40}
              y1={posterHeight - 80}
              x2={posterWidth - 40}
              y2={posterHeight - 80}
              stroke="#444"
              strokeWidth={1}
            />
            <SvgText
              x={posterWidth / 2}
              y={posterHeight - 40}
              fontSize="14"
              fill="#888"
              textAnchor="middle"
            >
              {t('yearEndSummary.appName')}
            </SvgText>
          </Svg>
        </View>
      
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={20} color="#ffffff" />
          <Text style={styles.actionButtonText}>{t('yearEndSummary.shareText')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleShareImage}>
          <Ionicons name="image-outline" size={20} color="#ffffff" />
          <Text style={styles.actionButtonText}>{t('yearEndSummary.shareImage')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleSaveImage}>
          <Ionicons name="download-outline" size={20} color="#ffffff" />
          <Text style={styles.actionButtonText}>{t('yearEndSummary.saveImage')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: '#ffffff',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  posterContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
  },
  poster: {
    borderRadius: 20,
    backgroundColor: '#1a1a2e',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: '#2196F3',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default YearEndSummaryPoster;
