import { useEffect } from 'react';
import { Stack } from "expo-router";
import { initDatabase } from './constants/Storage';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { TransactionProvider } from './context/TransactionContext';
import { CategoryProvider } from './context/CategoryContext';
import './i18n'; // 导入 i18n 配置
import i18n from './i18n'; // 导入 i18n 配置
import { SettingsProvider } from './context/SettingsContext';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import { crashReporter } from './utils/crashReporting';
import { ErrorBoundary } from './components/ErrorBoundary';

function AppStack() {
  const { theme } = useTheme();

  return (
    <SettingsProvider>
      <TransactionProvider>
        <CategoryProvider>
          <Stack
            screenOptions={{
              headerStyle: {
                backgroundColor: theme.surface,
              },
              headerTintColor: theme.textSecondary,
              headerTitleStyle: {
                fontWeight: 'bold',
                color: theme.text,
              },
            }}>
            <Stack.Screen name="index" 
            options={{ 
              headerShown: false,
              title: i18n.t('home.title')
             }
            } />
            <Stack.Screen name="screens/add" options={{
              headerShown: true,
              title: i18n.t('add.title'),
            }} />
            <Stack.Screen name="screens/categorySelect" options={{
              headerShown: false,
              title: i18n.t('categorySelect.title')
            }} />
            <Stack.Screen
            name="screens/categories"
              options={{
                headerShown: true,
                title: i18n.t('categories.title'),
              }}
            />
            <Stack.Screen
              name="screens/profile"
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="screens/budget"
              options={{
                headerShown: true,
                title: i18n.t('budget.title'),
              }}
            />
            <Stack.Screen
              name="screens/settings"
              options={{
                headerShown: true,
                title: i18n.t('settings.title'),
              }}
            />
            <Stack.Screen
              name="screens/statsDetail"
              options={{
                headerShown: true,
                title: i18n.t('stats.detail'),
              }}
            />
            <Stack.Screen
              name="screens/prepaidCards"
              options={{
                headerShown: true,
                title: i18n.t('prepaidCards.title'),
              }}
            />
            <Stack.Screen
              name="screens/shoppingPlatforms"
              options={{
                headerShown: true,
                title: i18n.t('shoppingPlatforms.title'),
              }}
            />
            <Stack.Screen
              name="screens/plannedPurchases"
              options={{
                headerShown: true,
                title: i18n.t('plannedPurchases.title'),
              }}
            />
            <Stack.Screen
              name="screens/tags"
              options={{
                headerShown: true,
                title: i18n.t('tags.title'),
              }}
            />
            <Stack.Screen
              name="screens/personalLending"
              options={{
                headerShown: true,
                title: i18n.t('lending.title'),
              }}
            />
            <Stack.Screen
              name="screens/lendingDetail"
              options={{
                headerShown: true,
                title: i18n.t('lending.recordDetail'),
              }}
            />
            <Stack.Screen
              name="screens/reimbursement"
              options={{
                headerShown: true,
                title: i18n.t('reimbursement.title'),
              }}
            />
          </Stack>
        </CategoryProvider>
      </TransactionProvider>
    </SettingsProvider>
  );
}

export default function RootLayout() {
  useEffect(() => {
    const init = async () => {
      try {
        // 初始化崩溃监控
        await crashReporter.initialize();
        crashReporter.logUserAction('App started');

        // 初始化数据库
        initDatabase();
        crashReporter.addBreadcrumb('Database initialized', 'info', 'system');
      } catch (error) {
        console.error('Failed to initialize app:', error);
        crashReporter.reportError(error instanceof Error ? error : new Error('App initialization failed'));
      }
    };
    init();
  }, []);

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        crashReporter.reportError(error, { errorInfo, location: 'RootLayout' });
      }}
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <ThemeProvider>
          <AppStack />
        </ThemeProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}
