{"name": "ninecents", "main": "expo-router/entry", "version": "1.1", "scripts": {"start": "expo start --dev-client", "start-original": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "build:ios": "eas build --platform ios --profile production", "build:ios-dev": "eas build --platform ios --profile development", "pods:install": "cd ios && pod install --repo-update", "pods:clean": "cd ios && rm -rf Pods Podfile.lock && pod install --repo-update", "clean": "rm -rf node_modules && npm install && npm run pods:clean"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "~14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "dayjs": "^1.11.13", "expo": "52", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.3", "expo-constants": "~17.0.8", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-in-app-purchases": "~14.5.0", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-mail-composer": "~14.0.2", "expo-media-picker": "^0.1.2", "expo-modules-core": "~2.2.3", "expo-router": "~4.0.21", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-sqlite": "~15.1.4", "expo-status-bar": "~2.0.1", "expo-store-review": "~8.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "i18n-js": "^4.5.1", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.7.2", "react-native": "0.76.9", "react-native-chart-kit": "^6.12.0", "react-native-draggable-flatlist": "^4.0.1", "react-native-gesture-handler": "~2.20.2", "react-native-localize": "^3.4.1", "react-native-maps": "1.18.0", "react-native-purchases": "^8.11.7", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.0", "@types/react-test-renderer": "^18.3.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "babel-plugin-transform-remove-console": "^6.9.4", "react-test-renderer": "18.3.1", "typescript": "~5.8.3", "web-streams-polyfill": "^4.1.0"}, "overrides": {"react": "18.3.1", "react-dom": "18.3.1", "@types/react": "~18.3.0", "@types/react-dom": "~18.3.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-chart-kit", "expo-in-app-purchases", "xlsx"]}}}, "private": true}